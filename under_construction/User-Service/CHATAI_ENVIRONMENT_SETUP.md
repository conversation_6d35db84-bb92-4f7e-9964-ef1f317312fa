# ChatAI Environment Configuration

## Required Environment Variables

For the ChatAI service to work with the complete document processing pipeline, you need to set the following environment variables:

### 🔑 **API Keys (Required for Full Functionality)**

```bash
# LlamaCloud API Key for document parsing and vector indexing
LLAMA_CLOUD_API_KEY=llx-your-llamacloud-api-key-here

# OpenRouter API Key for AI chat functionality
OPENROUTER_API_KEY=sk-or-your-openrouter-api-key-here
```

### 📋 **How to Get API Keys**

#### LlamaCloud API Key:
1. Visit [LlamaIndex Cloud](https://cloud.llamaindex.ai/)
2. Sign up or log in to your account
3. Navigate to API Keys section
4. Create a new API key
5. Copy the key (starts with `llx-`)

#### OpenRouter API Key:
1. Visit [OpenRouter](https://openrouter.ai/)
2. Sign up or log in to your account
3. Go to API Keys section
4. Create a new API key
5. Copy the key (starts with `sk-or-`)

### 🚀 **Setting Environment Variables**

#### For Railway Deployment:
1. Go to your Railway project dashboard
2. Navigate to Variables tab
3. Add the environment variables:
   - `LLAMA_CLOUD_API_KEY` = your LlamaCloud API key
   - `OPENROUTER_API_KEY` = your OpenRouter API key

#### For Local Development:
Create a `.env` file in the project root:
```bash
# Database
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USERNAME=your_username
POSTGRES_PASSWORD=your_password
POSTGRES_DATABASE=your_database

# ChatAI API Keys
LLAMA_CLOUD_API_KEY=llx-your-llamacloud-api-key-here
OPENROUTER_API_KEY=sk-or-your-openrouter-api-key-here

# Other required variables
JWT_SECRET=your-jwt-secret
RMQ_URL=amqp://localhost:5672
PORT=3000
```

### ✅ **Verification**

The services will automatically check for these API keys on startup:

- **LlamaCloud**: Used for document parsing (PDF, Office files) and vector indexing
- **OpenRouter**: Used for AI chat responses and document summarization

If keys are missing, the services will:
- Log warnings about missing functionality
- Gracefully degrade (basic text processing without AI features)
- Continue to work for other features

### 🔧 **Service Architecture**

With proper API keys configured, the ChatAI service provides:

1. **Document Upload & Processing**
   - File validation and security checks
   - LlamaParse for complex document parsing
   - Vector indexing with LlamaCloud
   - AI-powered document summarization

2. **Chat System**
   - RAG (Retrieval Augmented Generation)
   - Real-time streaming responses
   - Context from uploaded documents
   - Chat history management

3. **Security & Rate Limiting**
   - Per-user upload limits
   - AI usage tracking
   - Comprehensive error handling
   - Secure logging

### 📊 **Monitoring**

The service provides endpoints to monitor usage:
- `/users/app/chatai/get-credit-usage` - AI usage statistics
- Built-in rate limiting and abuse detection
- Comprehensive logging for debugging

### 🛡️ **Security Notes**

- API keys are stored as environment variables (never in code)
- All errors are sanitized before sending to clients
- Rate limiting prevents abuse
- File uploads are validated for security
- Comprehensive logging for audit trails

---

**Note**: The ChatAI service follows the exact same architecture as the reference server implementation, ensuring consistency and reliability.
